package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "DEMAND_FORECAST_PREDICTION_DATA")
public class DemandForecastPredictionData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "DEMAND_FORECAST_PREDICTION_ID")
    private Integer demandForecastPredictionId;

    @Column(name ="REFERENCE_ORDER_MENU_ITEM_ID")
    @ManyToOne(fetch = FetchType.LAZY, targetEntity = ReferenceOrderMenuItemData.class, optional = false)
    private ReferenceOrderMenuItemData menuItemDataId;

    @Column(name = "BUSINESS_DATE")
    private Date businessDate;

    @Column(name = "PREDICTED_QUANTITY")
    private Double predictedQuantity;

    @Column(name = "MEAN")
    private Double mean;

    @Column(name = "STANDARD_DEVIATION")
    private Double standardDeviation;

    @Column(name = "MEAN_METHOD")
    private String meanMethod;
}
